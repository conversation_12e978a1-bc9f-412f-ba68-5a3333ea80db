version: ${antora-component.version}
prerelease: ${antora-component.prerelease}

asciidoc:
  attributes:
    copyright-year: ${current.year}
    version: ${project.version}
    springversionshort: ${spring.short}
    springversion: ${spring}
    attribute-missing: 'warn'
    commons: ${springdata.commons.docs}
    lettuce: ${lettuce}
    jedis: ${jedis}
    include-xml-namespaces: false
    spring-data-commons-docs-url: https://docs.spring.io/spring-data/commons/reference
    spring-data-commons-javadoc-base: https://docs.spring.io/spring-data/commons/docs/${springdata.commons}/api/
    springdocsurl: https://docs.spring.io/spring-framework/reference/{springversionshort}
    springjavadocurl: https://docs.spring.io/spring-framework/docs/${spring}/javadoc-api
    spring-framework-docs: '{springdocsurl}'
    spring-framework-javadoc: '{springjavadocurl}'
    springhateoasversion: ${spring-hateoas}
    releasetrainversion: ${releasetrain}
    store: Redis

# PACKAGES antora@3.2.0-alpha.2 @antora/atlas-extension:1.0.0-alpha.1 @antora/collector-extension@1.0.0-alpha.3 @springio/antora-extensions@1.1.0-alpha.2 @asciidoctor/tabs@1.0.0-alpha.12 @opendevise/antora-release-line-extension@1.0.0-alpha.2
#
# The purpose of this Antora playbook is to build the docs in the current branch.
antora:
  extensions:
    - require: '@springio/antora-extensions'
      root_component_name: 'data-redis'
site:
  title: Spring Data Redis
  url: https://docs.spring.io/spring-data-redis/reference/
content:
  sources:
    - url: ./../../..
      branches: HEAD
      start_path: src/main/antora
      worktrees: true
    - url: https://github.com/spring-projects/spring-data-commons
      # Refname matching:
      # https://docs.antora.org/antora/latest/playbook/content-refname-matching/
      branches: [ main, 3.2.x ]
      start_path: src/main/antora
asciidoc:
  attributes:
    hide-uri-scheme: '@'
    tabs-sync-option: '@'
  extensions:
    - '@asciidoctor/tabs'
    - '@springio/asciidoctor-extensions'
    - '@springio/asciidoctor-extensions/javadoc-extension'
  sourcemap: true
urls:
  latest_version_segment: ''
runtime:
  log:
    failure_level: warn
    format: pretty
ui:
  bundle:
    url: https://github.com/spring-io/antora-ui-spring/releases/download/v0.4.16/ui-bundle.zip
    snapshot: true

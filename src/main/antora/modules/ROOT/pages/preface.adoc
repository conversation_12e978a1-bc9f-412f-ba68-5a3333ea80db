[[preface]]
= Preface

The Spring Data Redis project applies core Spring concepts to the development of solutions by using a key-value style data store.
We provide a "`template`" as a high-level abstraction for sending and receiving messages.
You may notice similarities to the JDBC support in the Spring Framework.

This section provides an easy-to-follow guide for getting started with the Spring Data Redis module.

[[get-started:first-steps:spring]]
== Learning Spring

Spring Data uses Spring framework's
{spring-framework-docs}/core.html[core] functionality, including:


* {spring-framework-docs}/core.html#beans[IoC] container
* {spring-framework-docs}/core.html#validation[type conversion system]
* {spring-framework-docs}/core.html#expressions[expression language]
* {spring-framework-docs}/integration.html#jmx[JMX integration]
* {spring-framework-docs}/data-access.html#dao-exceptions[DAO exception hierarchy].

While you need not know the Spring APIs, understanding the concepts behind them is important.
At a minimum, the idea behind Inversion of Control (IoC) should be familiar, and you should be familiar with whatever IoC container you choose to use.

The core functionality of the Redis support can be used directly, with no need to invoke the IoC services of the Spring Container.
This is much like `JdbcTemplate`, which can be used "'standalone'" without any other services of the Spring container.
To leverage all the features of Spring Data Redis, such as the repository support, you need to configure some parts of the library to use Spring.

To learn more about Spring, you can refer to the comprehensive documentation that explains the Spring Framework in detail.
There are a lot of articles, blog entries, and books on the subject.
See the Spring framework https://spring.io/projects/spring-framework/[home page] for more information.

In general, this should be the starting point for developers wanting to try Spring Data Redis.

[[get-started:first-steps:nosql]]
== Learning NoSQL and Key Value Stores

NoSQL stores have taken the storage world by storm.
It is a vast domain with a plethora of solutions, terms, and patterns (to make things worse, even the term itself has multiple https://www.google.com/search?q=nosoql+acronym[meanings]).
While some of the principles are common, it is crucial that you be familiar to some degree with the stores supported by SDR. The best way to get acquainted with these solutions is to read their documentation and follow their examples.
It usually does not take more then five to ten minutes to go through them and, if you come from an RDMBS-only background, many times these exercises can be eye-openers.

[[get-started:first-steps:samples]]
=== Trying out the Samples

One can find various samples for key-value stores in the dedicated Spring Data example repo, at https://github.com/spring-projects/spring-data-examples/tree/main/redis[https://github.com/spring-projects/spring-data-examples/].

[[requirements]]
== Requirements

Spring Data Redis binaries require JDK level 17 and above and https://spring.io/projects/spring-framework/[Spring Framework] {springVersion} and above.

In terms of key-value stores, https://redis.io[Redis] 2.6.x or higher is required.
Spring Data Redis is currently tested against the latest 6.0 release.

[[get-started:help]]
== Additional Help Resources

Learning a new framework is not always straightforward.
In this section, we try to provide what we think is an easy-to-follow guide for starting with the Spring Data Redis module.
However, if you encounter issues or you need advice, feel free to use one of the following links:

[get-started:help:community]]
Community Forum :: Spring Data on https://stackoverflow.com/questions/tagged/spring-data[Stack Overflow] is a tag for all Spring Data (not just Document) users to share information and help each other.
Note that registration is needed only for posting.

[[get-started:help:professional]]
Professional Support :: Professional, from-the-source support, with guaranteed response time, is available from https://pivotal.io/[Pivotal Sofware, Inc.], the company behind Spring Data and Spring.

[[get-started:up-to-date]]
== Following Development

For information on the Spring Data source code repository, nightly builds, and snapshot artifacts, see the Spring Data home https://spring.io/projects/spring-data/[page].

You can help make Spring Data best serve the needs of the Spring community by interacting with developers on Stack Overflow at either
https://stackoverflow.com/questions/tagged/spring-data[spring-data] or https://stackoverflow.com/questions/tagged/spring-data-redis[spring-data-redis].

If you encounter a bug or want to suggest an improvement (including to this documentation), please create a ticket on https://github.com/spring-projects/spring-data-redis/issues/new[Github].

To stay up to date with the latest news and announcements in the Spring eco system, subscribe to the Spring Community https://spring.io/[Portal].

Lastly, you can follow the Spring https://spring.io/blog/[blog] or the project team (https://twitter.com/SpringData[@SpringData]) on Twitter.

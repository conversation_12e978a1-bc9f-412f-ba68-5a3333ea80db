[[appendix]]
= Appendix

[[schema]]
== Schema

link:https://www.springframework.org/schema/redis/spring-redis-1.0.xsd[Spring Data Redis Schema (redis-namespace)]

[[supported-commands]]
== Supported Commands

.Redis commands supported by `RedisTemplate`
[width="50%",cols="<2,^1",options="header"]
|=========================================================
|Command |Template Support

|APPEND               |X
|AUTH                 |X
|BGREWRITEAOF         |X
|BGSAVE               |X
|BITCOUNT             |X
|BITFIELD             |X
|BITOP                |X
|BLPOP                |X
|BRPOP                |X
|BRPOPLPUSH           |X
|CLIENT KILL          |X
|CLIENT GETNAME       |X
|CLIENT LIST          |X
|CLIENT SETNAME       |X
|CLUSTER SLOTS        |-
|COMMAND              |-
|COMMAND COUNT        |-
|COMMAND GETKEYS      |-
|COMMAND INFO         |-
|CONFIG GET           |X
|CONFIG RESETSTAT     |X
|CONFIG REWRITE       |-
|CONFIG SET           |X
|DBSIZE               |X
|DEBUG OBJECT         |-
|DEBUG SEGFAULT       |-
|DECR                 |X
|DECRBY               |X
|DEL                  |X
|DISCARD              |X
|DUMP                 |X
|ECHO                 |X
|EVAL                 |X
|EVALSHA              |X
|EXEC                 |X
|EXISTS               |X
|EXPIRE               |X
|EXPIREAT             |X
|FLUSHALL             |X
|FLUSHDB              |X
|GEOADD               |X
|GEODIST              |X
|GEOHASH              |X
|GEOPOS               |X
|GEORADIUS            |X
|GEORADIUSBYMEMBER    |X
|GEOSEARCH            |X
|GEOSEARCHSTORE       |X
|GET                  |X
|GETBIT               |X
|GETRANGE             |X
|GETSET               |X
|HDEL                 |X
|HEXISTS              |X
|HEXPIRE              |X
|HEXPIREAT            |X
|HPEXPIRE             |X
|HPEXPIREAT           |X
|HPERSIST             |X
|HTTL                 |X
|HPTTL                |X
|HGET                 |X
|HGETALL              |X
|HINCRBY              |X
|HINCRBYFLOAT         |X
|HKEYS                |X
|HLEN                 |X
|HMGET                |X
|HMSET                |X
|HSCAN                |X
|HSET                 |X
|HSETNX               |X
|HVALS                |X
|INCR                 |X
|INCRBY               |X
|INCRBYFLOAT          |X
|INFO                 |X
|KEYS                 |X
|LASTSAVE             |X
|LINDEX               |X
|LINSERT              |X
|LLEN                 |X
|LPOP                 |X
|LPUSH                |X
|LPUSHX               |X
|LRANGE               |X
|LREM                 |X
|LSET                 |X
|LTRIM                |X
|MGET                 |X
|MIGRATE              |-
|MONITOR              |-
|MOVE                 |X
|MSET                 |X
|MSETNX               |X
|MULTI                |X
|OBJECT               |-
|PERSIST              |X
|PEXIPRE              |X
|PEXPIREAT            |X
|PFADD                |X
|PFCOUNT              |X
|PFMERGE              |X
|PING                 |X
|PSETEX               |X
|PSUBSCRIBE           |X
|PTTL                 |X
|PUBLISH              |X
|PUBSUB               |-
|PUBSUBSCRIBE         |-
|QUIT                 |X
|RANDOMKEY            |X
|RENAME               |X
|RENAMENX             |X
|REPLICAOF            |X
|RESTORE              |X
|ROLE                 |-
|RPOP                 |X
|RPOPLPUSH            |X
|RPUSH                |X
|RPUSHX               |X
|SADD                 |X
|SAVE                 |X
|SCAN                 |X
|SCARD                |X
|SCRIPT EXITS         |X
|SCRIPT FLUSH         |X
|SCRIPT KILL          |X
|SCRIPT LOAD          |X
|SDIFF                |X
|SDIFFSTORE           |X
|SELECT               |X
|SENTINEL FAILOVER    |X
|SENTINEL GET-MASTER-ADD-BY-NAME |-
|SENTINEL MASTER      | -
|SENTINEL MASTERS     |X
|SENTINEL MONITOR     |X
|SENTINEL REMOVE      |X
|SENTINEL RESET       |-
|SENTINEL SET         |-
|SENTINEL SLAVES      |X
|SET                  |X
|SETBIT               |X
|SETEX                |X
|SETNX                |X
|SETRANGE             |X
|SHUTDOWN             |X
|SINTER               |X
|SINTERSTORE          |X
|SISMEMBER            |X
|SLAVEOF              |X
|SLOWLOG              |-
|SMEMBERS             |X
|SMOVE                |X
|SORT                 |X
|SPOP                 |X
|SRANDMEMBER          |X
|SREM                 |X
|SSCAN                |X
|STRLEN               |X
|SUBSCRIBE            |X
|SUNION               |X
|SUNIONSTORE          |X
|SYNC                 |-
|TIME                 |X
|TTL                  |X
|TYPE                 |X
|UNSUBSCRIBE          |X
|UNWATCH              |X
|WATCH                |X
|ZADD                 |X
|ZCARD                |X
|ZCOUNT               |X
|ZINCRBY              |X
|ZINTERSTORE          |X
|ZLEXCOUNT            |-
|ZRANGE               |X
|ZRANGEBYLEX          |-
|ZREVRANGEBYLEX       |-
|ZRANGEBYSCORE        |X
|ZRANGESTORE          |X
|ZRANK                |X
|ZREM                 |X
|ZREMRANGEBYLEX       |-
|ZREMRANGEBYRANK      |X
|ZREVRANGE            |X
|ZREVRANGEBYSCORE     |X
|ZREVRANK             |X
|ZSCAN                |X
|ZSCORE               |X
|ZUNINONSTORE         |X
|=========================================================

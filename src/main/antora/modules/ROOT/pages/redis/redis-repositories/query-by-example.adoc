include::{commons}@data-commons::page$query-by-example.adoc[]

[[query-by-example.running]]
== Running an Example

The following example uses Query by Example against a repository:

.Query by Example using a Repository
====
[source, java]
----
interface PersonRepository extends ListQueryByExampleExecutor<Person> {
}

class PersonService {

  @Autowired PersonRepository personRepository;

  List<Person> findPeople(Person probe) {
    return personRepository.findAll(Example.of(probe));
  }
}
----
====

Redis Repositories support, with their secondary indexes, a subset of Spring Data's Query by Example features.
In particular, only exact, case-sensitive, and non-null values are used to construct a query.

Secondary indexes use set-based operations (Set intersection, Set union) to determine matching keys. Adding a property to the query that is not indexed returns no result, because no index exists. Query by Example support inspects indexing configuration to include only properties in the query that are covered by an index. This is to prevent accidental inclusion of non-indexed properties.

Case-insensitive queries and unsupported `StringMatcher` instances are rejected at runtime.

The following list shows the supported Query by Example options:

* Case-sensitive, exact matching of simple and nested properties
* Any/All match modes
* Value transformation of the criteria value
* Exclusion of `null` values from the criteria

The following list shows properties not supported by Query by Example:

* Case-insensitive matching
* Regex, prefix/contains/suffix String-matching
* Querying of Associations, Collection, and Map-like properties
* Inclusion of `null` values from the criteria
* `findAll` with sorting

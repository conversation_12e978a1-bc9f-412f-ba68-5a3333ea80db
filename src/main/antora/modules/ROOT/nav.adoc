* xref:index.adoc[Overview]
** xref:commons/upgrade.adoc[]
** xref:upgrading.adoc[]


* xref:redis.adoc[]
** xref:redis/getting-started.adoc[]
** xref:redis/drivers.adoc[]
** xref:redis/connection-modes.adoc[]
** xref:redis/template.adoc[RedisTemplate]
** xref:redis/redis-cache.adoc[]
** xref:redis/cluster.adoc[]
** xref:redis/hash-mappers.adoc[]
** xref:redis/pubsub.adoc[]
** xref:redis/redis-streams.adoc[]
** xref:redis/scripting.adoc[]
** xref:redis/transactions.adoc[]
** xref:redis/pipelining.adoc[]
** xref:redis/support-classes.adoc[]

* xref:repositories.adoc[]
** xref:repositories/core-concepts.adoc[]
** xref:repositories/definition.adoc[]
** xref:repositories/create-instances.adoc[]
** xref:redis/redis-repositories/usage.adoc[]
** xref:repositories/object-mapping.adoc[]
** xref:redis/redis-repositories/mapping.adoc[]
** xref:redis/redis-repositories/keyspaces.adoc[]
** xref:redis/redis-repositories/indexes.adoc[]
** xref:redis/redis-repositories/expirations.adoc[]
** xref:redis/redis-repositories/queries.adoc[]
** xref:redis/redis-repositories/query-by-example.adoc[]
** xref:redis/redis-repositories/cluster.adoc[]
** xref:redis/redis-repositories/anatomy.adoc[]
** xref:repositories/projections.adoc[]
** xref:repositories/custom-implementations.adoc[]
** xref:repositories/core-domain-events.adoc[]
** xref:repositories/null-handling.adoc[]
** xref:redis/redis-repositories/cdi-integration.adoc[]
** xref:repositories/query-keywords-reference.adoc[]
** xref:repositories/query-return-types-reference.adoc[]

* xref:observability.adoc[]

* xref:appendix.adoc[]


* xref:attachment$api/java/index.html[Javadoc,role=link-external,window=_blank]
* https://github.com/spring-projects/spring-data-commons/wiki[Wiki,role=link-external,window=_blank]

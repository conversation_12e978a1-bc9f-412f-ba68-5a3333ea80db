/*
 * Copyright 2015-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis;

import java.io.Serial;

import org.jspecify.annotations.Nullable;
import org.springframework.dao.DataRetrievalFailureException;

/**
 * {@link DataRetrievalFailureException} thrown when following cluster redirects exceeds the max number of edges.
 *
 * <AUTHOR>
 * @since 1.7
 */
public class TooManyClusterRedirectionsException extends DataRetrievalFailureException {

	private static final @Serial long serialVersionUID = -2818933672669154328L;

	/**
	 * Creates new {@link TooManyClusterRedirectionsException}.
	 *
	 * @param msg the detail message.
	 */
	public TooManyClusterRedirectionsException(@Nullable String msg) {
		super(msg);
	}

	/**
	 * Creates new {@link TooManyClusterRedirectionsException}.
	 *
	 * @param msg the detail message.
	 * @param cause the root cause from the data access API in use.
	 */
	public TooManyClusterRedirectionsException(@Nullable String msg, @Nullable Throwable cause) {
		super(msg, cause);
	}

}

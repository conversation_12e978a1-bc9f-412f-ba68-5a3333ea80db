/*
 * Copyright 2020-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.cache;

/**
 * Interface to be implemented by objects that expose {@link CacheStatistics} identified by {@code cacheName}. Typically
 * used by cache writers.
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2.4
 */
public interface CacheStatisticsProvider {

	/**
	 * Obtain snapshot of the captured statistics. May return a statistics object whose counters are zero if there are no
	 * statistics for {@code cacheName}.
	 *
	 * @param cacheName must not be {@literal null}.
	 * @return never {@literal null}.
	 */
	CacheStatistics getCacheStatistics(String cacheName);
}

/*
 * Copyright 2018-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.serializer;

import org.jspecify.annotations.Nullable;

/**
 * Raw {@link RedisSerializer} using {@code byte[]}.
 *
 * <AUTHOR>
 * @since 2.2
 */
enum ByteArrayRedisSerializer implements RedisSerializer<byte[]> {

	INSTANCE;

	@Override
	public byte[] serialize(byte @Nullable [] value) throws SerializationException {
		return value == null ? SerializationUtils.EMPTY_ARRAY : value;
	}


	@Override
	public byte @Nullable[] deserialize(byte @Nullable[] bytes) throws SerializationException {
		return bytes;
	}

}

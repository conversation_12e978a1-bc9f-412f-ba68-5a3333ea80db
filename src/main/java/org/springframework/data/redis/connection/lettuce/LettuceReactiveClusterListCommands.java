/*
 * Copyright 2016-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.connection.lettuce;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.ByteBuffer;

import org.reactivestreams.Publisher;
import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.data.redis.connection.ClusterSlotHashUtil;
import org.springframework.data.redis.connection.ReactiveClusterListCommands;
import org.springframework.data.redis.connection.ReactiveRedisConnection.ByteBufferResponse;
import org.springframework.util.Assert;

/**
 * <AUTHOR> <AUTHOR> @since 2.0
 */
class LettuceReactiveClusterListCommands extends LettuceReactiveListCommands implements ReactiveClusterListCommands {

	/**
	 * Create new {@link LettuceReactiveClusterListCommands}.
	 *
	 * @param connection must not be {@literal null}.
	 */
	LettuceReactiveClusterListCommands(LettuceReactiveRedisConnection connection) {
		super(connection);
	}

	@Override
	public Flux<PopResponse> bPop(Publisher<BPopCommand> commands) {

		return getConnection().execute(cmd -> Flux.from(commands).concatMap(command -> {

			Assert.notNull(command.getKeys(), "Keys must not be null");
			Assert.notNull(command.getDirection(), "Direction must not be null");

			if (ClusterSlotHashUtil.isSameSlotForAllKeys(command.getKeys())) {
				return super.bPop(Mono.just(command));
			}

			return Mono.error(new InvalidDataAccessApiUsageException("All keys must map to the same slot for BPOP command."));
		}));
	}

	@Override
	public Flux<ByteBufferResponse<RPopLPushCommand>> rPopLPush(Publisher<RPopLPushCommand> commands) {

		return getConnection().execute(cmd -> Flux.from(commands).concatMap(command -> {

			Assert.notNull(command.getKey(), "Key must not be null");
			Assert.notNull(command.getDestination(), "Destination key must not be null");

			if (ClusterSlotHashUtil.isSameSlotForAllKeys(command.getKey(), command.getDestination())) {
				return super.rPopLPush(Mono.just(command));
			}

			Mono<ByteBuffer> result = cmd.rpop(command.getKey())
					.flatMap(value -> cmd.lpush(command.getDestination(), value).map(x -> value));

			return result.map(value -> new ByteBufferResponse<>(command, value));
		}));
	}
}

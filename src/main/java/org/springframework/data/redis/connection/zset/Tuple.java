/*
 * Copyright 2022-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.connection.zset;

/**
 * ZSet tuple.
 */
public interface Tuple extends Comparable<Double> {

	/**
	 * @return the raw value of the member.
	 */
	byte[] getValue();

	/**
	 * @return the member score value used for sorting.
	 */
	Double getScore();

	/**
	 * Create a new {@link Tuple}.
	 *
	 * @param value
	 * @param score
	 * @return
	 */
	static Tuple of(byte[] value, Double score) {
		return new DefaultTuple(value, score);
	}
}

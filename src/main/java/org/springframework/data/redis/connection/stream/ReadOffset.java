/*
 * Copyright 2018-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.connection.stream;

import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * Value object representing read offset for a Stream.
 */
public final class ReadOffset {

	private final String offset;

	private ReadOffset(String offset) {
		this.offset = offset;
	}

	/**
	 * Read from the latest offset.
	 *
	 * @return
	 */
	public static ReadOffset latest() {
		return new ReadOffset("$");
	}

	/**
	 * Read all new arriving elements with ids greater than the last one consumed by the consumer group.
	 *
	 * @return the {@link ReadOffset} object without a specific offset.
	 */
	public static ReadOffset lastConsumed() {
		return new ReadOffset(">");
	}

	/**
	 * Read all arriving elements from the stream starting at {@code offset}.
	 *
	 * @param offset the stream offset.
	 * @return the {@link ReadOffset} starting at {@code offset}.
	 */
	public static ReadOffset from(String offset) {

		Assert.hasText(offset, "Offset must not be empty");

		return new ReadOffset(offset);
	}

	/**
	 * Read all arriving elements from the stream starting at {@link RecordId}. Using a
	 * {@link RecordId#shouldBeAutoGenerated() auto-generated} {@link RecordId} returns the {@link #latest()} read offset.
	 *
	 * @param offset the stream offset.
	 * @return the {@link ReadOffset} starting at {@link RecordId}.
	 */
	public static ReadOffset from(RecordId offset) {

		if (offset.shouldBeAutoGenerated()) {
			return latest();
		}

		return from(offset.getValue());
	}

	public String getOffset() {
		return this.offset;
	}

	@Override
	public boolean equals(@Nullable Object o) {

		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;

		ReadOffset that = (ReadOffset) o;

		return ObjectUtils.nullSafeEquals(offset, that.offset);
	}

	@Override
	public int hashCode() {
		return ObjectUtils.nullSafeHashCode(offset);
	}

	public String toString() {
		return "ReadOffset(offset=" + this.getOffset() + ")";
	}
}

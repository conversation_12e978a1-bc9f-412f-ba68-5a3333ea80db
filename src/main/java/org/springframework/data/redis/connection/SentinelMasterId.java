/*
 * Copyright 2021-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.connection;

import org.springframework.util.Assert;

/**
 * Simple {@link NamedNode}.
 *
 * <AUTHOR>
 * @since 2.5.3
 */
record SentinelMasterId(String name) implements NamedN<PERSON> {

	SentinelMasterId {
		Assert.hasText(name, "Sentinel Master Id must not be null or empty");
	}

	@Override
	public String getName() {
		return name;
	}

	@Override
	public String toString() {
		return name();
	}

}

/*
 * Copyright 2021-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.domain.geo;

import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Metric;
import org.springframework.util.Assert;

/**
 * Radius defined by {@link Distance}.
 *
 * <AUTHOR>
 * @since 2.6
 */
public class RadiusShape implements GeoShape {

	private final Distance radius;

	public RadiusShape(Distance radius) {

		Assert.notNull(radius, "Distance must not be null");

		this.radius = radius;
	}

	public Distance getRadius() {
		return radius;
	}

	@Override
	public Metric getMetric() {
		return radius.getMetric();
	}

}

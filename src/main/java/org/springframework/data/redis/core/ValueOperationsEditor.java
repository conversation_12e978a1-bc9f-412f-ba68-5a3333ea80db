/*
 * Copyright 2011-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.core;

import java.beans.PropertyEditorSupport;

/**
 * PropertyEditor allowing for easy injection of {@link ValueOperations} from {@link RedisOperations}.
 *
 * <AUTHOR>
 */
class ValueOperationsEditor extends PropertyEditorSupport {

	public void setValue(Object value) {
		if (value instanceof RedisOperations<?, ?> redisOperations) {
			super.setValue(redisOperations.opsForValue());
		} else {
			throw new IllegalArgumentException("Editor supports only conversion of type " + RedisOperations.class);
		}
	}
}

/*
 * Copyright 2015-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.core.convert;

import org.springframework.data.redis.core.index.ConfigurableIndexDefinitionProvider;

/**
 * {@link MappingConfiguration} is used for programmatic configuration of secondary indexes, key prefixes, expirations
 * and the such.
 *
 * <AUTHOR>
 * @since 1.7
 */
public class MappingConfiguration {

	private final ConfigurableIndexDefinitionProvider indexConfiguration;
	private final KeyspaceConfiguration keyspaceConfiguration;

	/**
	 * Creates new {@link MappingConfiguration}.
	 *
	 * @param indexConfiguration must not be {@literal null}.
	 * @param keyspaceConfiguration must not be {@literal null}.
	 */
	public MappingConfiguration(ConfigurableIndexDefinitionProvider indexConfiguration,
			KeyspaceConfiguration keyspaceConfiguration) {

		this.indexConfiguration = indexConfiguration;
		this.keyspaceConfiguration = keyspaceConfiguration;
	}

	/**
	 * @return never {@literal null}.
	 */
	public ConfigurableIndexDefinitionProvider getIndexConfiguration() {
		return indexConfiguration;
	}

	/**
	 * @return never {@literal null}.
	 */
	public KeyspaceConfiguration getKeyspaceConfiguration() {
		return keyspaceConfiguration;
	}

}

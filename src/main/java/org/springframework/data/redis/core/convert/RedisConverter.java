/*
 * Copyright 2015-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.core.convert;

import org.jspecify.annotations.Nullable;
import org.springframework.data.convert.EntityConverter;
import org.springframework.data.mapping.model.EntityInstantiators;
import org.springframework.data.redis.core.mapping.RedisMappingContext;
import org.springframework.data.redis.core.mapping.RedisPersistentEntity;
import org.springframework.data.redis.core.mapping.RedisPersistentProperty;

/**
 * Redis specific {@link EntityConverter}.
 *
 * <AUTHOR>
 * <AUTHOR> @since 1.7
 */
public interface RedisConverter
		extends EntityConverter<RedisPersistentEntity<?>, RedisPersistentProperty, Object, RedisData> {

	@Override
	RedisMappingContext getMappingContext();

	/**
	 * @return the configured {@link IndexResolver}, may be {@literal null}.
	 * @since 2.1
	 */
	@Nullable
	IndexResolver getIndexResolver();

	/**
	 * @return the configured {@link EntityInstantiators}.
	 * @since 3.2.4
	 */
	EntityInstantiators getEntityInstantiators();

}

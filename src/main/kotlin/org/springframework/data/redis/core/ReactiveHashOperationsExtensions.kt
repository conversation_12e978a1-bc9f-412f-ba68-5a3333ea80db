/*
 * Copyright 2019-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.core

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactive.awaitSingle

/**
 * Coroutines variant of [ReactiveHashOperations.hasKey].
 *
 * <AUTHOR>
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.hasKeyAndAwait(key: H, hashKey: HK): Boolean =
		hasKey(key, hashKey).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.get].
 *
 * <AUTHOR> Paluch
 * <AUTHOR> Strobl
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.getAndAwait(key: H, hashKey: HK): HV? =
		get(key, hashKey).awaitFirstOrNull()

/**
 * Coroutines variant of [ReactiveHashOperations.multiGet].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.multiGetAndAwait(key: H, vararg hashKeys: HK): List<HV?> =
		multiGet(key, hashKeys.toCollection(ArrayList())).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.increment].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.incrementAndAwait(key: H, hashKey: HK, delta: Long): Long =
		increment(key, hashKey, delta).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.keys].
 *
 * <AUTHOR> Deleuze
 * @since 2.2
 */
fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.keysAsFlow(key: H): Flow<HK> =
		keys(key).asFlow()

/**
 * Coroutines variant of [ReactiveHashOperations.increment].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.incrementAndAwait(key: H, hashKey: HK, delta: Double): Double =
		increment(key, hashKey, delta).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.size].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.sizeAndAwait(key: H): Long =
		size(key).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.putAll].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.putAllAndAwait(key: H, map: Map<HK, HV>): Boolean =
		putAll(key, map).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.put].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.putAndAwait(key: H, hashKey: HK, hashValue: HV): Boolean =
		put(key, hashKey, hashValue).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.putIfAbsent].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.putIfAbsentAndAwait(key: H, hashKey: HK, hashValue: HV): Boolean =
		putIfAbsent(key, hashKey, hashValue).awaitSingle()

/**
 * Coroutines variant of [ReactiveHashOperations.values].
 *
 * <AUTHOR> Deleuze
 * @since 2.2
 */
fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.valuesAsFlow(key: H): Flow<HV> =
		values(key).asFlow()

/**
 * Coroutines variant of [ReactiveHashOperations.entries].
 *
 * <AUTHOR> Deleuze
 * @since 2.2
 */
fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.entriesAsFlow(key: H): Flow<Map.Entry<HK, HV>> =
		entries(key).asFlow()

/**
 * Coroutines variant of [ReactiveHashOperations.scan].
 *
 * <AUTHOR> Deleuze
 * @since 2.2
 */
fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.scanAsFlow(key: H, options: ScanOptions = ScanOptions.NONE): Flow<Map.Entry<HK, HV>> =
		scan(key, options).asFlow()

/**
 * Coroutines variant of [ReactiveHashOperations.remove].
 *
 * <AUTHOR> Paluch
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.removeAndAwait(key: H, vararg hashKeys: Any): Long =
		remove(key, *hashKeys).awaitSingle()

/**
 * Coroutines variant of [ReactiveListOperations.delete].
 *
 * <AUTHOR> Strobl
 * @since 2.2
 */
suspend fun <H : Any, HK : Any, HV : Any> ReactiveHashOperations<H, HK, HV>.deleteAndAwait(key: H): Boolean =
		delete(key).awaitSingle()

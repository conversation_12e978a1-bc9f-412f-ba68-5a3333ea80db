<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="lettuceConnectionFactory "
	      class="org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory">
		<property name="hostName">
			<bean class="org.springframework.data.redis.SettingsUtils"
			      factory-method="getHost"/>
		</property>
		<property name="port">
			<bean class="org.springframework.data.redis.SettingsUtils"
			      factory-method="getPort"/>
		</property>
		<property name="pipeliningFlushPolicy">
			<bean class="org.springframework.data.redis.connection.lettuce.LettuceConnection$PipeliningFlushPolicy"
			      factory-method="flushOnClose"/>
		</property>
		<property name="shutdownTimeout" value="0"/>
		<property name="clientResources">
			<bean class="org.springframework.data.redis.test.extension.LettuceTestClientResources"
			      factory-method="getSharedClientResources"/>
		</property>
	</bean>

</beans>

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd">


	<bean id="jedisConnectionFactory"
		class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory" p:timeout="60000" p:usePool="false">
		<property name="hostName">
			<bean class="org.springframework.data.redis.SettingsUtils"
				factory-method="getHost" />
		</property>
		<property name="port">
			<bean class="org.springframework.data.redis.SettingsUtils"
				factory-method="getPort" />
		</property>
		<property name="clientName" value="jedis-client" />
	</bean>

</beans>
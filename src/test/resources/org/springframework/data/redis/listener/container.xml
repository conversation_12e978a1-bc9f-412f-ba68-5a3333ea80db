<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

	<bean id="messageListener"
		class="org.springframework.data.redis.listener.adapter.MessageListenerAdapter">
		<constructor-arg>
			<bean class="org.springframework.data.redis.listener.adapter.RedisMDP" />
		</constructor-arg>
	</bean>

	<bean id="connectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"/>
	
	<bean id="redisContainer"
		class="org.springframework.data.redis.listener.RedisMessageListenerContainer">
		<property name="connectionFactory" ref="connectionFactory" />
		<property name="messageListeners">
			<map>
				<entry key-ref="messageListener">
					<bean class="org.springframework.data.redis.listener.ChannelTopic">
						<constructor-arg value="chatroom" />
					</bean>
				</entry>
			</map>
		</property>
	</bean>

</beans>

/*
 * Copyright 2011-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis;

import java.io.Serializable;

import org.jspecify.annotations.Nullable;

/**
 * Simple serializable class.
 *
 * <AUTHOR>
 * <AUTHOR> <PERSON>u
 */
public class Person implements Serializable {

	private static final long serialVersionUID = 92633004015631981L;

	private String firstName;
	private String lastName;

	private Integer age;
	private Address address;

	public Person() {}

	public Person(String firstName, String lastName, int age) {
		this(firstName, lastName, age, null);
	}

	public Person(String firstName, String lastName, int age, Address address) {
		super();
		this.firstName = firstName;
		this.lastName = lastName;
		this.age = age;
		this.address = address;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public int getAge() {
		return age;
	}

	public void setAge(int age) {
		this.age = age;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((age == null) ? 0 : age.hashCode());
		result = prime * result + ((firstName == null) ? 0 : firstName.hashCode());
		result = prime * result + ((lastName == null) ? 0 : lastName.hashCode());
		return result;
	}

	public boolean equals(@Nullable Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof Person that))
			return false;
		if (address == null) {
			if (that.address != null)
				return false;
		} else if (!address.equals(that.address))
			return false;
		if (age == null) {
			if (that.age != null)
				return false;
		} else if (!age.equals(that.age))
			return false;
		if (firstName == null) {
			if (that.firstName != null)
				return false;
		} else if (!firstName.equals(that.firstName))
			return false;
		if (lastName == null) {
			if (that.lastName != null)
				return false;
		} else if (!lastName.equals(that.lastName))
			return false;
		return true;
	}
}

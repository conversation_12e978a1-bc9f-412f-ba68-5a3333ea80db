/*
 * Copyright 2021-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.util;

import static org.assertj.core.api.Assertions.*;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link ByteUtils}.
 *
 * <AUTHOR>
 */
class ByteUtilsUnitTests {

	@Test // GH-2204
	void getBytesShouldUseCorrectHeapBufferSpace() {

		ByteBuffer buffer = ByteBuffer.allocate(16);
		buffer.put("hello".getBytes(StandardCharsets.US_ASCII));
		buffer.flip();

		byte[] bytes = ByteUtils.getBytes(buffer);

		assertThat(bytes).hasSize(5);
	}

	@Test // GH-2204
	void getBytesShouldUseCorrectDirectBufferSpace() {

		ByteBuffer buffer = ByteBuffer.allocateDirect(16);
		buffer.put("hello".getBytes(StandardCharsets.US_ASCII));
		buffer.flip();

		byte[] bytes = ByteUtils.getBytes(buffer);

		assertThat(bytes).hasSize(5);
	}
}

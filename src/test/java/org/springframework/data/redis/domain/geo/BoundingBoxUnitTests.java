/*
 * Copyright 2023-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.domain.geo;

import static org.assertj.core.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.data.geo.Distance;

/**
 * Unit tests for {@link BoundingBox}.
 *
 * <AUTHOR>
 */
class BoundingBoxUnitTests {

	@Test // GH-2526
	void shouldReturnCorrectValues() {

		Distance w = new Distance(1);
		Distance h = new Distance(2);
		BoundingBox box = new BoundingBox(w, h);

		assertThat(box.getWidth()).isEqualTo(w);
		assertThat(box.getHeight()).isEqualTo(h);
	}

	@Test // GH-2526
	void shouldEqual() {

		Distance w = new Distance(1);
		Distance h = new Distance(2);
		BoundingBox box1 = new BoundingBox(w, h);
		BoundingBox box2 = new BoundingBox(w, h);

		assertThat(box1).isEqualTo(box2).hasSameHashCodeAs(box2);
	}
}

/*
 * Copyright 2017-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.connection;

import static org.assertj.core.api.Assertions.*;

import java.util.Properties;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link RedisServer}.
 *
 * <AUTHOR>
 */
class RedisServerUnitTests {

	@Test // DATAREDIS-618
	void shouldReadNumberOfOtherSentinelsCorrectly() {

		RedisServer redisServer = RedisServer.newServerFrom(createProperties());

		assertThat(redisServer.getNumberOtherSentinels()).isEqualTo(2L);
	}

	private Properties createProperties() {

		Properties map = new Properties();

		map.put("num-other-sentinels", "2");

		return map;
	}
}

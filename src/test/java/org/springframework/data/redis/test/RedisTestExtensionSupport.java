/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.test;

import org.junit.jupiter.api.extension.ExtensionContext;

/**
 * Support class for Redis test extensions.
 *
 * <AUTHOR>
 */
public abstract class RedisTestExtensionSupport {

	/**
	 * Returns the session {@link ExtensionContext.Store} from the given {@link ExtensionContext} and
	 * {@link ExtensionContext.Namespace}
	 *
	 * @param context
	 * @param namespace
	 * @return
	 */
	protected ExtensionContext.Store getSessionStore(ExtensionContext context, ExtensionContext.Namespace namespace) {
		return context.getRoot().getStore(ExtensionContext.StoreScope.LAUNCHER_SESSION, namespace);
	}
}

/*
 * Copyright 2020-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.data.redis.test;

import org.springframework.data.redis.serializer.OxmSerializer;
import org.springframework.oxm.xstream.XStreamMarshaller;

import com.thoughtworks.xstream.security.AnyTypePermission;

/**
 * <AUTHOR>
 */
public final class XstreamOxmSerializerSingleton {

	private static final XstreamOxmSerializerSingleton instance = new XstreamOxmSerializerSingleton();

	private final OxmSerializer serializer;

	private XstreamOxmSerializerSingleton() {

		// XStream serializer
		XStreamMarshaller xstream = new XStreamMarshaller();
		xstream.getXStream().addPermission(AnyTypePermission.ANY);
		try {
			xstream.afterPropertiesSet();
		} catch (Exception ex) {
			throw new RuntimeException("Cannot init XStream", ex);
		}

		serializer = new OxmSerializer(xstream, xstream);
		serializer.afterPropertiesSet();
	}

	public static OxmSerializer getInstance() {
		return instance.serializer;
	}
}

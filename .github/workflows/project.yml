# GitHub Actions to automate GitHub issues for Spring Data Project Management

name: Spring Data GitHub Issues

on:
  issues:
    types: [opened, edited, reopened]
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened, edited, reopened]

jobs:
  Inbox:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'spring-projects' && (github.event.action == 'opened' || github.event.action == 'reopened') && github.event.pull_request == null && !contains(join(github.event.issue.labels.*.name, ', '), 'dependency-upgrade') && !contains(github.event.issue.title, 'Release ')
    steps:
      - name: Create or Update Issue Card
        uses: actions/add-to-project@v1.0.2
        with:
          project-url: https://github.com/orgs/spring-projects/projects/25
          github-token: ${{ secrets.GH_ISSUES_TOKEN_SPRING_DATA }}
  Pull-Request:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'spring-projects' && (github.event.action == 'opened' || github.event.action == 'reopened') && github.event.pull_request != null
    steps:
      - name: Create or Update Pull Request Card
        uses: actions/add-to-project@v1.0.2
        with:
          project-url: https://github.com/orgs/spring-projects/projects/25
          github-token: ${{ secrets.GH_ISSUES_TOKEN_SPRING_DATA }}
  Feedback-Provided:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'spring-projects' && github.event_name == 'issue_comment' && github.event.action == 'created' && github.actor != 'spring-projects-issues' && github.event.pull_request == null && github.event.issue.state == 'open' && contains(toJSON(github.event.issue.labels), 'waiting-for-feedback')
    steps:
      - name: Update Project Card
        uses: actions/add-to-project@v1.0.2
        with:
          project-url: https://github.com/orgs/spring-projects/projects/25
          github-token: ${{ secrets.GH_ISSUES_TOKEN_SPRING_DATA }}
